# 🚀 Role-Based Chatbot Deployment Guide

## 🎯 Overview

Due to Snowflake Streamlit limitations, dynamic role switching using `USE ROLE` statements is not supported. Instead, you need to deploy separate instances of the app for each role, or deploy the app using the specific role you want to filter data for.

## ⚠️ Important: Role Switching Limitation

**Error Message:** `Unsupported statement type 'USE'`

**Root Cause:** Streamlit apps running inside Snowflake cannot execute `USE ROLE` statements dynamically.

**Solution:** Deploy the app using the specific role you want to use for data filtering.

## 🔧 Deployment Options

### Option 1: Single Role Deployment (Recommended)

Deploy the app using a specific regional role to automatically filter data for that region.

#### Steps:
1. **Switch to your desired role in Snowflake:**
   ```sql
   USE ROLE HRZN_WESTERN_US_HEAD;  -- Example for Western US
   ```

2. **Deploy the Streamlit app:**
   ```sql
   -- Create or replace the Streamlit app
   CREATE OR REPLACE STREAMLIT "TEST_DB"."PUBLIC"."SALESBOT_WESTERN_US"
   ROOT_LOCATION = '@"TEST_DB"."PUBLIC"."MY_STAGE"'
   MAIN_FILE = 'app.py'
   QUERY_WAREHOUSE = 'COMPUTE_WH';
   ```

3. **Grant access to the app:**
   ```sql
   GRANT USAGE ON STREAMLIT "TEST_DB"."PUBLIC"."SALESBOT_WESTERN_US" TO ROLE HRZN_WESTERN_US_HEAD;
   ```

### Option 2: Multiple Role Deployments

Deploy separate app instances for different regions.

#### Example for Multiple Regions:
```sql
-- Western US App
USE ROLE HRZN_WESTERN_US_HEAD;
CREATE OR REPLACE STREAMLIT "TEST_DB"."PUBLIC"."SALESBOT_WESTERN_US"
ROOT_LOCATION = '@"TEST_DB"."PUBLIC"."MY_STAGE"'
MAIN_FILE = 'app.py'
QUERY_WAREHOUSE = 'COMPUTE_WH';

-- Eastern US App  
USE ROLE HRZN_EASTERN_US_HEAD;
CREATE OR REPLACE STREAMLIT "TEST_DB"."PUBLIC"."SALESBOT_EASTERN_US"
ROOT_LOCATION = '@"TEST_DB"."PUBLIC"."MY_STAGE"'
MAIN_FILE = 'app.py'
QUERY_WAREHOUSE = 'COMPUTE_WH';

-- Caribbean App
USE ROLE HRZN_CARIBBEAN_HEAD;
CREATE OR REPLACE STREAMLIT "TEST_DB"."PUBLIC"."SALESBOT_CARIBBEAN"
ROOT_LOCATION = '@"TEST_DB"."PUBLIC"."MY_STAGE"'
MAIN_FILE = 'app.py'
QUERY_WAREHOUSE = 'COMPUTE_WH';
```

## 🎯 How the App Works Now

### Role Selection Interface:
- **Display Purpose**: The role selector in the sidebar now serves as a reference/display tool
- **Educational**: Shows users which regions are available and what role they would need
- **Current Role Display**: Shows the actual active role the app is running under
- **Clear Messaging**: Explains that data filtering is based on the deployment role

### Data Filtering:
- **Automatic**: Row access policies automatically filter data based on the deployment role
- **Transparent**: The app clearly shows which role is active and which region's data is displayed
- **Secure**: Filtering is enforced at the database level by Snowflake

## 📋 User Experience

### What Users See:
1. **Active Role Display**: Shows the current Snowflake role the app is running under
2. **Region Information**: Displays which region's data will be shown
3. **Role Selector**: Educational tool showing available roles (display only)
4. **Test Functionality**: "Test Current Role" button to verify data access
5. **Query Results**: Clear indication of which region's data is being displayed

### Sample User Interface:
```
🔐 Role Selection
ℹ️ How Role-Based Filtering Works:
• Deploy this app using the specific role you want to use
• Row access policies will automatically filter data based on the deployment role
• The role selector below shows which region's data you'll see

Select your role: [Western US (HRZN_WESTERN_US_HEAD)]
[Set Display Role]

🎯 Active Snowflake Role: Western US (HRZN_WESTERN_US_HEAD)
This is the role your app is currently running under - data will be filtered for this region.

[🔍 Test Current Role]
```

## 🔍 Testing Your Deployment

### Verify Role-Based Filtering:
1. **Check Current Role**: Use the "Test Current Role" button
2. **Run Sample Queries**: Ask questions like "Show me total sales by region"
3. **Verify Filtering**: Confirm only your region's data is returned
4. **Test Different Tables**: Verify access to SALES, RETURN, and REPRESENTATIVES tables

### Sample Test Queries:
```
"Show me total sales by region"
"What are the top 10 products by sales?"
"How many sales representatives do we have?"
"Show me return rates by product"
```

## 🛠️ Troubleshooting

### Issue: No Data Returned
**Possible Causes:**
- No data exists for your region
- Row access policies are too restrictive
- Wrong role used for deployment

**Solutions:**
1. Check if data exists for your region using an admin role
2. Verify row access policies are correctly configured
3. Redeploy using the correct regional role

### Issue: Wrong Region Data
**Cause:** App deployed using wrong role

**Solution:** Redeploy the app using the correct regional role

### Issue: Access Denied Errors
**Possible Causes:**
- Role doesn't have access to required tables
- Missing database/schema permissions

**Solutions:**
1. Grant necessary permissions to the role
2. Verify role has access to TEST_DB.PUBLIC schema
3. Check row access policy configuration

## 📝 Best Practices

### For Administrators:
1. **Document Role Mappings**: Keep clear documentation of which roles map to which regions
2. **Standardize Naming**: Use consistent naming for app instances (e.g., SALESBOT_[REGION])
3. **Grant Permissions**: Ensure each role has proper access to required tables
4. **Test Thoroughly**: Verify row access policies work correctly for each role

### For Users:
1. **Use Correct App**: Access the app instance deployed for your region
2. **Verify Role**: Always check the "Active Snowflake Role" display
3. **Test Access**: Use the "Test Current Role" button to verify data access
4. **Understand Filtering**: Remember that data is automatically filtered by region

## 🎯 Summary

The role-based chatbot now works by:
1. **Deploying** the app using specific regional roles
2. **Automatically filtering** data based on the deployment role
3. **Clearly displaying** which role is active and which region's data is shown
4. **Providing testing tools** to verify access and filtering

This approach ensures secure, role-based data access while working within Snowflake Streamlit's limitations.
